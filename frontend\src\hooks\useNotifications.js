import { useState, useEffect, useCallback } from 'react'
import { useSnackbar } from 'notistack'
import useApi from './useApi'
import { useSocket } from '../context/SocketContext'

const useNotifications = () => {
  const [notifications, setNotifications] = useState([])
  const [unreadCount, setUnreadCount] = useState(0)
  const { enqueueSnackbar } = useSnackbar()
  const { socket } = useSocket()

  // Fetch notifications
  const {
    data: notificationsData,
    loading: notificationsLoading,
    error: notificationsError,
    execute: fetchNotifications,
    refetch: refetchNotifications
  } = useApi('/api/notifications', {
    immediate: true,
    transform: (response) => response.data || response.notifications || []
  })

  // Update local state when data changes
  useEffect(() => {
    if (notificationsData) {
      setNotifications(notificationsData)
      const unread = notificationsData.filter(n => !n.read).length
      setUnreadCount(unread)
    }
  }, [notificationsData])

  // Socket listeners for real-time notifications
  useEffect(() => {
    if (!socket) return

    const handleNewNotification = (notification) => {
      setNotifications(prev => [notification, ...prev])
      setUnreadCount(prev => prev + 1)
      
      // Show snackbar for new notification
      enqueueSnackbar(notification.message, {
        variant: 'info',
        autoHideDuration: 4000,
      })
    }

    const handleNotificationRead = (notificationId) => {
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId ? { ...n, read: true } : n
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))
    }

    socket.on('notification', handleNewNotification)
    socket.on('notification_read', handleNotificationRead)

    return () => {
      socket.off('notification', handleNewNotification)
      socket.off('notification_read', handleNotificationRead)
    }
  }, [socket, enqueueSnackbar])

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId) => {
    try {
      await useApi(`/api/notifications/${notificationId}/read`, {
        method: 'PUT',
        immediate: false
      }).execute()

      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId ? { ...n, read: true } : n
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))

    } catch (error) {
      enqueueSnackbar('Failed to mark notification as read', { variant: 'error' })
    }
  }, [enqueueSnackbar])

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      await useApi('/api/notifications/mark-all-read', {
        method: 'PUT',
        immediate: false
      }).execute()

      setNotifications(prev => 
        prev.map(n => ({ ...n, read: true }))
      )
      setUnreadCount(0)

      enqueueSnackbar('All notifications marked as read', { variant: 'success' })

    } catch (error) {
      enqueueSnackbar('Failed to mark all notifications as read', { variant: 'error' })
    }
  }, [enqueueSnackbar])

  // Delete notification
  const deleteNotification = useCallback(async (notificationId) => {
    try {
      await useApi(`/api/notifications/${notificationId}`, {
        method: 'DELETE',
        immediate: false
      }).execute()

      setNotifications(prev => prev.filter(n => n.id !== notificationId))
      
      // Update unread count if the deleted notification was unread
      const deletedNotification = notifications.find(n => n.id === notificationId)
      if (deletedNotification && !deletedNotification.read) {
        setUnreadCount(prev => Math.max(0, prev - 1))
      }

      enqueueSnackbar('Notification deleted', { variant: 'success' })

    } catch (error) {
      enqueueSnackbar('Failed to delete notification', { variant: 'error' })
    }
  }, [notifications, enqueueSnackbar])

  // Clear all notifications
  const clearAll = useCallback(async () => {
    try {
      await useApi('/api/notifications/clear-all', {
        method: 'DELETE',
        immediate: false
      }).execute()

      setNotifications([])
      setUnreadCount(0)

      enqueueSnackbar('All notifications cleared', { variant: 'success' })

    } catch (error) {
      enqueueSnackbar('Failed to clear notifications', { variant: 'error' })
    }
  }, [enqueueSnackbar])

  // Get notifications by type
  const getNotificationsByType = useCallback((type) => {
    return notifications.filter(n => n.type === type)
  }, [notifications])

  // Get unread notifications
  const getUnreadNotifications = useCallback(() => {
    return notifications.filter(n => !n.read)
  }, [notifications])

  return {
    notifications,
    unreadCount,
    loading: notificationsLoading,
    error: notificationsError,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
    refetch: refetchNotifications,
    getNotificationsByType,
    getUnreadNotifications
  }
}

export default useNotifications
