{"hash": "2ac3a745", "configHash": "b812562d", "lockfileHash": "68975434", "browserHash": "404d6333", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "1cdc52ee", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "157328e7", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "eea10e4f", "needsInterop": false}, "@mui/material": {"src": "../../@mui/material/esm/index.js", "file": "@mui_material.js", "fileHash": "6ae107eb", "needsInterop": false}, "@emotion/react": {"src": "../../@emotion/react/dist/emotion-react.browser.development.esm.js", "file": "@emotion_react.js", "fileHash": "db7c26c0", "needsInterop": false}, "@emotion/styled": {"src": "../../@emotion/styled/dist/emotion-styled.browser.development.esm.js", "file": "@emotion_styled.js", "fileHash": "b5f23135", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "85980225", "needsInterop": false}, "socket.io-client": {"src": "../../socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "41d831cb", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "87510e4f", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "ad621a93", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "ab84d8f2", "needsInterop": true}}, "chunks": {"chunk-K5R7YULI": {"file": "chunk-K5R7YULI.js"}, "chunk-JNNNAK6O": {"file": "chunk-JNNNAK6O.js"}, "chunk-A7ECLLTJ": {"file": "chunk-A7ECLLTJ.js"}, "chunk-QKH7HSXQ": {"file": "chunk-QKH7HSXQ.js"}, "chunk-EZYHN6DO": {"file": "chunk-EZYHN6DO.js"}, "chunk-EQCCHGRT": {"file": "chunk-EQCCHGRT.js"}, "chunk-HSUUC2QV": {"file": "chunk-HSUUC2QV.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}