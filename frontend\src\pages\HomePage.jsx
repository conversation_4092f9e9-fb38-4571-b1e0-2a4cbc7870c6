import { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Grid,
  Fab,
  useTheme,
  useMediaQuery
} from '@mui/material'
import {
  Add,
  TrendingUp,
  People,
  Home as HomeIcon
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../context/AuthContext'
import { usePosts } from '../hooks'
import { PostCard, PostForm } from '../components/social'
import { LoadingSpinner, ErrorBoundary } from '../components/ui'

const HomePage = () => {
  const [activeTab, setActiveTab] = useState(0)
  const [showCreatePost, setShowCreatePost] = useState(false)

  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const { user } = useAuth()
  const navigate = useNavigate()

  const {
    posts,
    loading,
    error,
    createPost,
    toggleLike,
    fetchPosts
  } = usePosts()

  // Fetch posts based on active tab
  useEffect(() => {
    const fetchType = activeTab === 0 ? 'all' : activeTab === 1 ? 'following' : 'trending'
    fetchPosts(fetchType)
  }, [activeTab, fetchPosts])

  const handleCreatePost = async (postData) => {
    try {
      await createPost(postData)
      setShowCreatePost(false)
    } catch (error) {
      console.error('Error creating post:', error)
    }
  }

  const handlePostAction = (action, postId) => {
    switch (action) {
      case 'like':
        toggleLike(postId)
        break
      case 'comment':
        // Handle comment action
        break
      case 'share':
        // Handle share action
        break
      case 'edit':
        navigate(`/create?edit=${postId}`)
        break
      case 'delete':
        // Handle delete action
        break
      default:
        break
    }
  }

  const tabs = [
    { label: 'For You', icon: HomeIcon },
    { label: 'Following', icon: People },
    { label: 'Trending', icon: TrendingUp }
  ]

  if (loading) {
    return <LoadingSpinner message="Loading your feed..." />
  }

  if (error) {
    return (
      <Box textAlign="center" py={6}>
        <Typography variant="h6" gutterBottom>
          Failed to load posts
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          {error}
        </Typography>
      </Box>
    )
  }

  return (
    <ErrorBoundary>
      <Container maxWidth="lg" sx={{ py: 2 }}>
        {/* Header */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 3,
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 2
        }}>
          <Typography
            variant={isMobile ? "h5" : "h4"}
            component="h1"
            fontWeight="bold"
            sx={{
              background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}
          >
            Welcome back, {user?.name?.split(' ')[0]}!
          </Typography>

          {!isMobile && (
            <Fab
              color="primary"
              size="medium"
              onClick={() => setShowCreatePost(true)}
              sx={{
                boxShadow: 3,
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
              }}
            >
              <Add />
            </Fab>
          )}
        </Box>

        {/* Feed Tabs */}
        <Tabs
          value={activeTab}
          onChange={(_, newValue) => setActiveTab(newValue)}
          sx={{
            mb: 3,
            '& .MuiTab-root': {
              borderRadius: 2,
              mx: 0.5,
              '&.Mui-selected': {
                bgcolor: 'primary.main',
                color: 'primary.contrastText'
              }
            }
          }}
          variant={isMobile ? "fullWidth" : "standard"}
          centered={!isMobile}
        >
          {tabs.map((tab, index) => {
            const Icon = tab.icon
            return (
              <Tab
                key={index}
                label={tab.label}
                icon={<Icon />}
                iconPosition="start"
                sx={{
                  minHeight: 48,
                  fontWeight: 600
                }}
              />
            )
          })}
        </Tabs>

        {/* Posts Feed */}
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            {posts.length === 0 ? (
              <Box textAlign="center" py={6}>
                <Typography variant="h6" gutterBottom>
                  No posts yet
                </Typography>
                <Typography variant="body1" color="text.secondary" gutterBottom>
                  {activeTab === 1
                    ? "Start following people to see their posts here!"
                    : "Be the first to create a post!"
                  }
                </Typography>
                <Fab
                  variant="extended"
                  color="primary"
                  onClick={() => setShowCreatePost(true)}
                  sx={{ mt: 2 }}
                >
                  <Add sx={{ mr: 1 }} />
                  Create Post
                </Fab>
              </Box>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                {posts.map((post) => (
                  <PostCard
                    key={post.id || post._id}
                    post={post}
                    onLike={() => handlePostAction('like', post.id || post._id)}
                    onComment={() => handlePostAction('comment', post.id || post._id)}
                    onShare={() => handlePostAction('share', post.id || post._id)}
                    onEdit={() => handlePostAction('edit', post.id || post._id)}
                    onDelete={() => handlePostAction('delete', post.id || post._id)}
                    onSave={() => handlePostAction('save', post.id || post._id)}
                    onReport={() => handlePostAction('report', post.id || post._id)}
                  />
                ))}
              </Box>
            )}
          </Grid>

          {/* Right Sidebar - Only show on desktop */}
          {!isMobile && (
            <Grid item md={4}>
              <Box sx={{ position: 'sticky', top: 20 }}>
                {/* Quick Create Post */}
                <Box sx={{ mb: 3 }}>
                  <PostForm
                    onSubmit={handleCreatePost}
                    compact
                    placeholder="What's on your mind?"
                  />
                </Box>
              </Box>
            </Grid>
          )}
        </Grid>

        {/* Mobile Create Post FAB */}
        {isMobile && (
          <Fab
            color="primary"
            sx={{
              position: 'fixed',
              bottom: 80,
              right: 16,
              background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
            }}
            onClick={() => setShowCreatePost(true)}
          >
            <Add />
          </Fab>
        )}

        {/* Create Post Dialog */}
        {showCreatePost && (
          <PostForm
            open={showCreatePost}
            onClose={() => setShowCreatePost(false)}
            onSubmit={handleCreatePost}
            fullScreen={isMobile}
          />
        )}
      </Container>
    </ErrorBoundary>
  )
}

export default HomePage
