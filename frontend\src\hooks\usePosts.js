import { useState, useCallback } from 'react'
import { useSnackbar } from 'notistack'
import useApi from './useApi'
import postsService from '../services/postsService'

const usePosts = () => {
  const [posts, setPosts] = useState([])
  const [likedPosts, setLikedPosts] = useState(new Set())
  const { enqueueSnackbar } = useSnackbar()

  // Fetch posts with different filters
  const {
    data: allPosts,
    loading: postsLoading,
    error: postsError,
    execute: fetchPosts,
    refetch: refetchPosts
  } = useApi('/api/posts', {
    immediate: false,
    transform: (response) => response.data || response.posts || []
  })

  // Create post
  const createPost = useCallback(async (postData) => {
    try {
      const newPost = await postsService.createPost(postData)
      setPosts(prev => [newPost, ...prev])
      enqueueSnackbar('Post created successfully!', { variant: 'success' })
      return newPost
    } catch (error) {
      enqueueSnackbar('Failed to create post', { variant: 'error' })
      throw error
    }
  }, [enqueueSnackbar])

  // Update post
  const updatePost = useCallback(async (postId, updateData) => {
    try {
      const updatedPost = await postsService.updatePost(postId, updateData)
      setPosts(prev => prev.map(post => 
        post.id === postId ? { ...post, ...updatedPost } : post
      ))
      enqueueSnackbar('Post updated successfully!', { variant: 'success' })
      return updatedPost
    } catch (error) {
      enqueueSnackbar('Failed to update post', { variant: 'error' })
      throw error
    }
  }, [enqueueSnackbar])

  // Delete post
  const deletePost = useCallback(async (postId) => {
    try {
      await postsService.deletePost(postId)
      setPosts(prev => prev.filter(post => post.id !== postId))
      enqueueSnackbar('Post deleted successfully!', { variant: 'success' })
    } catch (error) {
      enqueueSnackbar('Failed to delete post', { variant: 'error' })
      throw error
    }
  }, [enqueueSnackbar])

  // Like/Unlike post
  const toggleLike = useCallback(async (postId) => {
    try {
      const isLiked = likedPosts.has(postId)
      
      if (isLiked) {
        await postsService.unlikePost(postId)
        setLikedPosts(prev => {
          const newSet = new Set(prev)
          newSet.delete(postId)
          return newSet
        })
      } else {
        await postsService.likePost(postId)
        setLikedPosts(prev => new Set(prev).add(postId))
      }

      // Update post likes count
      setPosts(prev => prev.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            likesCount: isLiked ? post.likesCount - 1 : post.likesCount + 1,
            isLiked: !isLiked
          }
        }
        return post
      }))

    } catch (error) {
      enqueueSnackbar('Failed to update like', { variant: 'error' })
      throw error
    }
  }, [likedPosts, enqueueSnackbar])

  // Fetch posts by type
  const fetchPostsByType = useCallback(async (type = 'all') => {
    const endpoints = {
      all: '/api/posts',
      following: '/api/feed/following',
      trending: '/api/feed/trending',
      user: (userId) => `/api/posts/user/${userId}`
    }

    const endpoint = typeof endpoints[type] === 'function' 
      ? endpoints[type] 
      : endpoints[type] || endpoints.all

    return fetchPosts(endpoint)
  }, [fetchPosts])

  // Fetch user posts
  const fetchUserPosts = useCallback(async (userId) => {
    return fetchPosts(`/api/posts/user/${userId}`)
  }, [fetchPosts])

  return {
    posts: allPosts || posts,
    loading: postsLoading,
    error: postsError,
    likedPosts,
    createPost,
    updatePost,
    deletePost,
    toggleLike,
    fetchPosts: fetchPostsByType,
    fetchUserPosts,
    refetchPosts
  }
}

export default usePosts
